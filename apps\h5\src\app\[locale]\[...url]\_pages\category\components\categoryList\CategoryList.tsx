'use client'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  encodeBase64,
  type HomeProduct,
  isBase64,
  type RedirectType,
  resolveCatchMessage,
  type TabItems,
  type TCatchMessage,
  TRACK_EVENT,
  updateProductList,
  useCurrentTime,
  useGetCategoryProductsQuery,
  usePagination,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Button } from 'antd-mobile'

import {
  BannerCardSkeleton,
  CustomEmpty,
  CustomNavBar,
  Footer,
  Header,
  ProductCard,
  ProductCardSkeleton,
  ScrollNavTabs,
} from '@/components'
import { CustomImage } from '@/components/common'

import { CategoryFilter } from '../categoryFilter'
import { CategorySkeleton } from '../categorySkeleton'

/**
 * 分类页面
 * 该页面名称/banner区域需要SSR渲染，以下部分可CSR渲染
 */
const CategoryPage = ({ uid }: { uid: string }) => {
  const categoryUid = decodeURIComponent(uid)

  const { page, pageSize, handlePageChange, handlePageNext } = usePagination(1, 24)
  const toast = useToastContext()
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const getI18nString = useTranslations('Common')

  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()

  const [activeCategoryUid, setActiveCategoryUid] = useState(
    isBase64(categoryUid) ? categoryUid : encodeBase64(categoryUid),
  )
  const [categoryTitle, setCategoryTitle] = useState('')
  const [catalogCategoryNav, setCatalogCategoryNav] = useState<
    {
      title: string | null | undefined
      value: string | undefined
      sort: number
    }[]
  >([])
  const [sort, setSort] = useState<Record<string, 'ASC' | 'DESC'>>({
    position: 'ASC',
  })
  const [banner, setBanner] = useState<{
    title?: string | null | undefined
    subtitle?: string | null | undefined
    image?: string | null | undefined
    description?: string | null | undefined
    buttonText?: string | null | undefined
    url?:
      | {
          type?: RedirectType | null
          url?: string | null
          value?: string | null
        }
      | null
      | undefined
    widthRatio: number | null | undefined
    heightRatio: number | null | undefined
  } | null>(null)

  const [products, setProducts] = useState<HomeProduct[]>([])
  const [hasMore, setHasMore] = useState(false)
  const [showEmpty, setShowEmpty] = useState(false)
  const [onlyMyCar, setOnlyMyCar] = useState(false) // 仅显示我的车型
  const [isDigital, setIsDigital] = useState(false) // 是否是皮肤音效分类

  const {
    currentData: categoriesAllData,
    isLoading,
    isFetching,
    error,
  } = useGetCategoryProductsQuery(
    {
      filters: {
        category_uid: {
          eq: activeCategoryUid,
        },
      },
      pageSize,
      currentPage: page,
      sort,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
      productFilter: {
        user_device: {
          eq: onlyMyCar ? '1' : '0',
        },
      },
    },
    {
      skip: !activeCategoryUid,
    },
  )

  useEffect(() => {
    handlePageChange(1)
    setHasMore(false)
    setBanner(null)
    setProducts([])
  }, [sort, handlePageChange, onlyMyCar])

  const handleTabChange = (item: {
    title: string | null | undefined
    value: string | undefined
    sort: number
  }) => {
    // 埋点：分类专区顶部tab点击
    reportEvent(TRACK_EVENT.shop_sort_tab_click, {
      category_id: item?.value || '',
      category_name: item?.title || '',
    })

    setCategoryTitle(item?.title as string)
    setActiveCategoryUid(item?.value as string)
    setSort({
      position: 'ASC',
    })
    handlePageChange(1)
    setHasMore(false)
    setBanner(null)
    setProducts([])
  }

  // const handleRefresh = async () => {
  //   setIsRefreshing(true);
  //   setHasMore(false);
  //   try {
  //     if (page === 1) {
  //       await refetchCategoryProducts();
  //     } else {
  //       handlePageChange(1);
  //       setBanner(null);
  //       setProducts([]);
  //     }
  //     setIsRefreshing(false);
  //   } catch {
  //     setIsRefreshing(false);
  //   }
  // };

  useEffect(() => {
    if (categoriesAllData?.categories?.items?.length) {
      const currentCategory = categoriesAllData.categories.items[0]
      setCategoryTitle(currentCategory?.name || '')

      const newProducts = currentCategory?.products?.items || []
      const pageInfo = currentCategory?.products?.page_info || {}

      setProducts((prevProducts) => updateProductList(prevProducts, newProducts as HomeProduct[]))

      setBanner({
        title: currentCategory?.category_banner_title,
        subtitle: currentCategory?.category_banner_subtitle,
        image: currentCategory?.category_mobile_banner,
        description: currentCategory?.category_banner_description,
        buttonText: currentCategory?.category_banner_button_text,
        url: currentCategory?.category_banner_url,
        widthRatio: currentCategory?.category_mobile_banner_width || 1,
        heightRatio: currentCategory?.category_mobile_banner_height || 1,
      })
      setIsDigital(Boolean(currentCategory?.is_digital) || false)

      if (!!pageInfo?.total_pages && !!pageInfo?.current_page) {
        setHasMore(pageInfo.total_pages > pageInfo.current_page)
      }
    }
  }, [categoriesAllData])

  useEffect(() => {
    if (categoriesAllData?.categories?.items?.length && !catalogCategoryNav.length) {
      const currentCategory = categoriesAllData.categories.items[0]
      const subCatalogCategoryAll = currentCategory?.children?.filter(
        (item) => item?.include_in_menu === 1,
      )
      if (subCatalogCategoryAll?.length && !catalogCategoryNav.length) {
        const newSubCatalogCategoryAll = subCatalogCategoryAll.map((item, index) => ({
          title: item?.name || '',
          value: item?.uid || '',
          sort: index + 1,
        }))
        newSubCatalogCategoryAll.unshift({
          title: getI18nString('plp_all'),
          value: categoriesAllData?.categories?.items?.[0]?.uid || '',
          sort: 0,
        })
        setCatalogCategoryNav(newSubCatalogCategoryAll)
      }
    }
  }, [categoriesAllData, catalogCategoryNav, getI18nString])

  useEffect(() => {
    if (isLoading || isFetching) {
      setHasMore(false)
      setShowEmpty(false)
    } else {
      setShowEmpty(products.length === 0)
    }
  }, [isLoading, isFetching, products])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  useEffect(() => {
    if (error) {
      toast.show({
        content:
          (resolveCatchMessage(error as TCatchMessage) as string) ||
          getI18nString('fetch_data_error'),
        icon: 'fail',
      })
    }
  }, [error, toast, getI18nString])

  useEffect(() => {
    if (activeCategoryUid && categoryTitle && categoryTitle !== getI18nString('all')) {
      // 埋点：点击分类类别
      reportEvent(TRACK_EVENT.shop_small_classfication_exposure, {
        category_id: activeCategoryUid,
        category_name: categoryTitle,
      })
    }
  }, [activeCategoryUid, categoryTitle, reportEvent, getI18nString])

  return (
    <>
      <Header />
      {/* 顶部导航 */}
      <CustomNavBar
        title={categoryTitle}
        customHeaderStyle="h-base-48"
        onBack={() => {
          console.log('返回按钮被点击')
          try {
            // 检查是否有历史记录可以返回
            if (window.history.length > 1) {
              window.history.back()
              console.log('使用 window.history.back() 返回成功')
            } else {
              // 如果没有历史记录，跳转到首页
              console.log('没有历史记录，跳转到首页')
              openPage({ route: 'home' })
            }
          } catch (error) {
            console.error('返回操作失败:', error)
            // 发生错误时跳转到首页
            openPage({ route: 'home' })
          }
          return false // 阻止默认的 router.back()
        }}
      />

      {isLoading ? (
        <CategorySkeleton />
      ) : (
        <>
          <div className="sticky top-base-48 z-50 bg-white">
            {/* 分类导航 */}
            <ScrollNavTabs
              tabs={catalogCategoryNav as TabItems[]}
              defaultActiveKey="0"
              mode="dropdown"
              showSort={true}
              onTabChange={(key) => {
                const currentNav = catalogCategoryNav.find((item) => item.sort === Number(key))
                if (currentNav) {
                  handleTabChange(currentNav)
                }
              }}
              position="left"
            />
            <div className="px-base-16 pb-[16px]">
              {/* 筛选组件 */}
              <CategoryFilter
                sort={sort}
                setSort={setSort}
                isDigital={isDigital}
                onlyMyCar={onlyMyCar}
                setOnlyMyCar={setOnlyMyCar}
              />
            </div>
          </div>

          <div className="px-base-16 pb-base-16">
            {/* 商品列表 */}
            {banner?.image ? (
              <div
                className="relative mb-[16px] w-full rounded-[12px] bg-gray-base"
                onClick={() => {
                  openPage({ ...banner.url })
                }}>
                {banner.image && (
                  <>
                    <CustomImage
                      style={{
                        width: '100%',
                        height: '100%',
                        overflow: 'hidden',
                        borderRadius: 12,
                        aspectRatio: `${banner.widthRatio || 1}/${banner.heightRatio || 1}`,
                      }}
                      src={banner.image}
                      fit="cover"
                      alt={banner?.title || ''}
                    />
                    <div className="absolute top-[16px] flex flex-col items-start">
                      {banner.subtitle ? (
                        <div className="ml-[16px] flex items-center justify-center rounded-[100px] bg-[#DA291C] px-[16px] py-[8px]">
                          <div className="truncate text-center font-miSansSemibold520 text-[12px] text-[#FFFFFF]">
                            {banner.subtitle}
                          </div>
                        </div>
                      ) : null}
                      <div className="mt-[12px] truncate px-[16px] text-center font-miSansSemibold520 text-[18px] leading-[22px] text-[#FFFFFF]">
                        {banner.title}
                      </div>
                      <div className="mt-[6px] truncate px-[16px] font-miSansSemibold520 text-[18px] leading-[22px] text-[#FFFFFF]">
                        {banner.description}
                      </div>
                      <div className="mt-[21px] flex flex-row items-center justify-center px-[16px]">
                        <div className="font-miSansSemibold520 text-[16px] leading-[19px] text-[#FFFFFF]">
                          {banner.buttonText}
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : null}
            {isFetching ? (
              <div className="flex flex-col">
                {!products.length && <BannerCardSkeleton />}
                <ProductCardSkeleton length={6} />
              </div>
            ) : products.length > 0 ? (
              // 商品列表
              <>
                <div className="grid grid-cols-2 gap-x-[12px] gap-y-[28px]">
                  {products.map((item) => (
                    <ProductCard
                      key={item.sku}
                      product={item}
                      currentTime={currentTime}
                      containerStyle="mb-0"
                      pageType="sort"
                    />
                  ))}
                </div>

                {isFetching ? null : hasMore ? (
                  <div className="my-base-24 flex items-center justify-center">
                    <Button className="nb-button" onClick={handlePageNext} disabled={isFetching}>
                      <div className="text-center font-miSansDemiBold450 text-[13px] leading-[16px] text-[#000000]">
                        {getI18nString('more')}
                      </div>
                    </Button>
                  </div>
                ) : (
                  <div className="mt-[36px] h-base-32 text-center text-base leading-[32px] text-[#6E6E73]">
                    {getI18nString("It's the end")}
                  </div>
                )}
              </>
            ) : (
              showEmpty && (
                <CustomEmpty style="h-[133px] flex flex-col items-center justify-center mt-[32px] mb-[64px]" />
              )
            )}
          </div>
        </>
      )}
      <Footer />
    </>
  )
}

export default CategoryPage

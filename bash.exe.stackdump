Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBD7BC0000 ntdll.dll
7FFBD68B0000 KERNEL32.DLL
7FFBD5130000 KERNELBASE.dll
7FFBD72E0000 USER32.dll
7FFBD5660000 win32u.dll
7FFBD7B50000 GDI32.dll
7FFBD5520000 gdi32full.dll
7FFBD5080000 msvcp_win.dll
7FFBD5810000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBD6560000 advapi32.dll
7FFBD79F0000 msvcrt.dll
7FFBD7230000 sechost.dll
7FFBD74B0000 RPCRT4.dll
7FFBD4220000 CRYPTBASE.DLL
7FFBD4DA0000 bcryptPrimitives.dll
7FFBD7B10000 IMM32.DLL

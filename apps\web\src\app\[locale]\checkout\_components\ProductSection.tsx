'use client'

import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslations } from 'next-intl'
import { yupResolver } from '@hookform/resolvers/yup'
import { OrderPickupStore, useToastContext } from '@ninebot/core'
import { pickUpCommentSchema } from '@ninebot/core/src/validations'
import { Input } from 'antd'
import { Controller, useForm } from 'react-hook-form'

import { CustomInput } from '@/components/common'
import { CartItem } from '@/types/checkout'

import { Clear } from '../_icons'

import CheckoutProduct from './CheckoutProduct'

interface FormData {
  telephone: string
  comment?: string
}

interface ProductSectionRef {
  validate: () => Promise<{ [key: string]: string | number | boolean } | null>
  scrollTo: () => Promise<void>
}

interface ProductSectionProps {
  showType?: 'express' | 'pickup'
  scrollViewRef?: React.RefObject<HTMLDivElement>
  title: string
  products: CartItem[]
}

// 使用 forwardRef 包装组件，正确处理 ref 传递
export default forwardRef<ProductSectionRef, ProductSectionProps>(function ProductTypeList(
  { showType = 'express', scrollViewRef, title, products },
  ref,
) {
  const toast = useToastContext()
  const rootRef = useRef<HTMLDivElement>(null)
  const getI18nString = useTranslations('Common')

  /**
   * 提供表单服务
   */
  const {
    control,
    trigger,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    mode: 'onBlur',
    resolver: yupResolver(pickUpCommentSchema),
    defaultValues: {
      telephone: '',
      comment: '',
    },
  })

  /**
   * 获取店铺信息
   */
  const pickupStoreInfo = useMemo(() => {
    return products?.length
      ? (products[0]?.extension_info as { store_info?: OrderPickupStore })?.store_info
      : null
  }, [products])

  /**
   * 自定义输入框失焦验证
   */
  const onBlurHandler = useCallback(
    async (name: keyof FormData) => {
      clearErrors()
      await trigger(name)
    },
    [clearErrors, trigger],
  )

  /**
   * 生成提交数据
   */
  const generateSubmitData = useCallback(
    (formData: FormData) => {
      const items = products.map((product) => ({
        item_id: product.uid,
        telephone: formData.telephone || '',
        comment: formData.comment || '',
      }))
      return {
        items: JSON.stringify(items),
      }
    },
    [products],
  )

  /**
   * 表单 errors 提示
   */
  useEffect(() => {
    if (errors && Object.keys(errors).length) {
      const firstErrorKey = Object.keys(errors)[0] as keyof FormData
      const firstError = errors[firstErrorKey]
      if (firstError?.message) {
        toast.show({
          content: String(firstError.message),
        })
      }
    }
  }, [errors, toast])

  /**
   * 暴露 Api
   */
  useImperativeHandle(ref, () => {
    return {
      /**
       * 验证表单
       */
      validate: async () => {
        const isValid = await trigger()
        if (isValid) {
          return generateSubmitData(getValues())
        }
        return null
      },
      /**
       * 滚动到表单位置
       */
      scrollTo: async () => {
        if (rootRef.current && scrollViewRef?.current) {
          const rect = rootRef.current.getBoundingClientRect()
          scrollViewRef.current.scrollTo({
            top: rect.top - 8,
            behavior: 'smooth',
          })
        }
      },
    }
  })

  const [commentLength, setCommentLength] = useState(0)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const measureRef = useRef<HTMLDivElement>(null)
  const inputContainerRef = useRef<HTMLDivElement>(null)

  // 检查输入框是否溢出
  const checkOverflow = useCallback((text: string) => {
    if (measureRef.current && inputContainerRef.current) {
      // 设置测量元素的文本内容
      measureRef.current.textContent = text

      // 获取实际可用宽度（输入框宽度减去右侧留出的空间和左侧padding）
      const containerWidth = inputContainerRef.current.clientWidth - 95 - 16

      // 获取测量元素的宽度
      const textWidth = measureRef.current.clientWidth

      // 如果文本宽度大于可用宽度，则视为溢出
      setIsOverflowing(textWidth > containerWidth)
    }
  }, [])

  return (
    <div className="bg-white" ref={rootRef}>
      <div className="flex flex-col gap-base-24">
        <h2 className="font-miSansDemiBold450 text-[22px] leading-none">{title}</h2>

        {/* 商品列表 */}
        {products.map((product) => (
          <CheckoutProduct
            key={product.uid}
            product={product.product}
            extension_info={product.extension_info}
            quantity={product.quantity}
          />
        ))}
      </div>
      {/* 自提信息输入 */}
      {showType === 'pickup' && (
        <div className="mt-16 flex flex-col gap-base-16">
          {/* 用于测量文本宽度的隐藏元素 */}
          <div
            ref={measureRef}
            style={{
              position: 'absolute',
              visibility: 'hidden',
              whiteSpace: 'nowrap',
              fontSize: '14px', // 与输入框字体大小保持一致
              fontFamily: 'inherit',
            }}
          />
          {/* 自提门店信息 */}
          {pickupStoreInfo ? (
            <div className="font-miSansRegular330 text-[16px] leading-none">
              自提门店:{pickupStoreInfo.store_address}
            </div>
          ) : null}

          <div className="w-full">
            <Controller
              control={control}
              name="telephone"
              render={({ field: { onChange, value } }) => (
                <CustomInput
                  type="number"
                  label="手机号码"
                  labelClassName="text-[#00000066]"
                  leftIcon={<div className="h-[12px] w-[1px] bg-primary" />}
                  rightIcon={<Clear />}
                  onRightIconClick={() => {
                    onChange('')
                    trigger('telephone')
                  }}
                  required
                  maxLength={11}
                  placeholder={getI18nString('only_china_phone')}
                  onBlur={() => onBlurHandler('telephone')}
                  onChange={(e) => {
                    let value = e.target.value
                    if (value.length > 11) {
                      // 如果字符长度超过11，则截断
                      value = value.slice(0, 11)
                    }
                    onChange(value)
                    if (!value) {
                      trigger('telephone')
                    }
                  }}
                  value={value || ''}
                  error={!!errors.telephone}
                  errorMessage={errors.telephone?.message as string}
                />
              )}
            />
          </div>

          <div className="w-full">
            <Controller
              control={control}
              name="comment"
              render={({ field: { onChange, value } }) => (
                <div className="relative" ref={inputContainerRef}>
                  <Input
                    className="h-[54px]"
                    style={{
                      paddingLeft: '16px',
                      paddingRight: '95px',
                    }}
                    maxLength={500}
                    placeholder="留言"
                    onBlur={() => onBlurHandler('comment')}
                    onChange={(e) => {
                      const newValue = e.target.value || ''
                      onChange(newValue)
                      setCommentLength(newValue.length)
                      // 检测文本是否溢出
                      checkOverflow(newValue)
                    }}
                    value={value || ''}
                  />
                  <div
                    className={`absolute right-0 top-1/2 flex h-[46px] w-[95px] -translate-y-1/2 items-center justify-center p-base-12 text-lg text-[#BBBBBD] ${isOverflowing ? 'shadow-[-5px_0_5px_-3px_rgba(0,0,0,0.15)]' : ''}`}>
                    {commentLength}/500
                  </div>
                </div>
              )}
            />
          </div>
        </div>
      )}
    </div>
  )
})

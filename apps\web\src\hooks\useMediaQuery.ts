'use client'

import { useEffect, useState } from 'react'
import { configResponsive, useResponsive } from 'ahooks'

// Configure responsive breakpoints 与 tailwind 一致
configResponsive({
  xs: 375,
  sm: 640,
  md: 768,
  lg: 960,
  xl: 1024,
  xll: 1200,
  '2xl': 1440,
  '3xl': 1920,
})

type ResponsiveInfo = Record<string, boolean>

/**
 * 媒体查询
 */
const useMediaQuery = () => {
  const responsive = useResponsive()

  const [media, setMedia] = useState<ResponsiveInfo>()

  useEffect(() => {
    setMedia({
      xs: responsive?.['xs'],
      sm: responsive?.['sm'],
      md: responsive?.['md'],
      lg: responsive?.['lg'],
      xl: responsive?.['xl'],
      xll: responsive?.['xll'],
      '2xl': responsive?.['2xl'],
      '3xl': responsive?.['3xl'],
    })
  }, [responsive])

  return media
}

export default useMediaQuery

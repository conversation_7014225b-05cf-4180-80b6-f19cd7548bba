/**
 * 渲染 HTML
 * @param {object} props - 组件参数
 * @param {string} props.content - HTML 内容
 * @param {number} props.width - 宽度
 * @param {number} props.center - 居中
 */

import './style.css'
const RenderHtml = ({
  content,
  width,
  center,
  contentStyle = '',
}: {
  content: string
  width?: number
  center?: boolean
  contentStyle?: string
}) => {
  return (
    <div
      className={`${width ? `w-[${width}px]` : 'w-full'} ${center ? 'flex flex-col items-center' : ''} render-html-container ${contentStyle}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

export default RenderHtml

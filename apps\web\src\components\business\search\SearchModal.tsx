'use client'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  appLocalStorage,
  storeConfigSelector,
  TRACK_EVENT,
  usePagination,
  useSearchHotWordsQuery,
  useSearchRelationWordsQuery,
  useVolcAnalytics,
} from '@ninebot/core'
import {
  GetSearchHotWordsQuery,
  GetSearchRelationWordsQuery,
} from '@ninebot/core/src/graphql/generated/graphql'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Modal } from 'antd'
import type { RefSelectProps } from 'antd/es/select'

import { Change, IconArrow, RecommendProducts, SearchInput, Trash } from '@/components'
import { useRouter } from '@/i18n/navigation'

// 添加常量
const SEARCH_HISTORY_KEY = 'search_history'
const MAX_HISTORY_ITEMS = 30

const IconDelete = ({ color = '#86868B' }) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.97248 3.06744L3.06738 3.97253L6.68777 7.59292L7.59287 7.59292L7.59287 6.68782L3.97248 3.06744Z"
        fill={color}
      />
      <path
        d="M3.06756 12.1185L3.97266 13.0236L7.59304 9.40317L7.59304 8.49808L6.68795 8.49808L3.06756 12.1185Z"
        fill={color}
      />
      <path
        d="M13.0242 12.1185L12.1191 13.0236L8.49875 9.40317L8.49875 8.49808L9.40385 8.49808L13.0242 12.1185Z"
        fill={color}
      />
      <path
        d="M13.0242 3.9726L12.1191 3.0675L8.49875 6.68789L8.49875 7.59299L9.40385 7.59299L13.0242 3.9726Z"
        fill={color}
      />
    </svg>
  )
}

type HotSearchList = NonNullable<NonNullable<GetSearchHotWordsQuery['search_hot_words']>>['items']

type SearchSuggestions = NonNullable<
  NonNullable<GetSearchRelationWordsQuery['search_relation_words']>
>['items']

interface SearchModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [searchValue, setSearchValue] = useState('')
  const [isRotating, setIsRotating] = useState(false)
  const inputRef = useRef<RefSelectProps>(null)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false)
  const router = useRouter()
  const { reportEvent } = useVolcAnalytics()
  const historyContainerRef = useRef<HTMLDivElement>(null)
  const [needsCollapse, setNeedsCollapse] = useState(false)

  const storeConfig = useAppSelector(storeConfigSelector)
  const defaultSearchWord = storeConfig?.default_search_words || ''

  const getI18nString = useTranslations('Common')
  const { page, pageSize, handlePageChange, handlePageNext } = usePagination(1, 6)
  const { data: hotSearchData, isFetching } = useSearchHotWordsQuery({
    currentPage: page,
    pageSize,
  })
  const { data: relationWords } = useSearchRelationWordsQuery(
    {
      searchWords: searchValue?.trim(),
      currentPage: 1,
      pageSize: 9999,
    },
    { skip: !searchValue?.trim() },
  )
  const [suggestions, setSuggestions] = useState<SearchSuggestions>([])
  const [hotSearchList, setHotSearchList] = useState<HotSearchList>([])
  const [totalPages, setTotalPages] = useState(1)
  const [isDelete, setIsDelete] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    if (hotSearchData?.search_hot_words?.items?.length) {
      setHotSearchList(hotSearchData?.search_hot_words?.items)
      setTotalPages(hotSearchData?.search_hot_words?.page_info?.total_pages as number)
    }
  }, [hotSearchData])

  useEffect(() => {
    if (
      relationWords?.search_relation_words?.items &&
      relationWords?.search_relation_words?.items?.length > 0
    ) {
      setSuggestions(relationWords.search_relation_words.items)
    } else {
      setSuggestions([])
    }
  }, [relationWords])

  const handleInputChange = (value: string) => {
    if (!value) {
      setSuggestions([])
    }
    setSearchValue(value)
  }

  const handleRefreshClick = () => {
    reportEvent(TRACK_EVENT.shop_searchpage_switch_products_click, {
      button_id: 'shop_change_trending keywords',
    })
    if (page < totalPages) {
      handlePageNext()
    } else {
      handlePageChange(1)
    }
  }

  const getSearchHistory = useCallback(async () => {
    const history = await appLocalStorage.getItem(SEARCH_HISTORY_KEY)
    if (history) {
      setSearchHistory(JSON.parse(history as string))
    }
  }, [])

  const handleClear = () => {
    setIsExpanded(false)
  }

  // 当弹窗打开时自动聚焦输入框
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // 在组件内添加状态
  useEffect(() => {
    // 从localStorage加载搜索历史
    getSearchHistory()
  }, [getSearchHistory])

  // 添加更新搜索历史的函数
  const updateSearchHistory = async (keyword: string) => {
    if (!keyword.trim()) return

    const newHistory = [keyword, ...searchHistory.filter((item) => item !== keyword)].slice(
      0,
      MAX_HISTORY_ITEMS,
    )

    setSearchHistory(newHistory)
    await appLocalStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory))
  }

  // 清空功能
  const clearSearchHistory = () => {
    setIsConfirmModalOpen(true)
  }

  // 删除单个搜索历史
  const handleItemDelete = async (value: string) => {
    handleClear()
    const newSearchHistoryList = searchHistory.filter((item) => item !== value)
    setSearchHistory(newSearchHistoryList)
    await appLocalStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newSearchHistoryList))
  }

  const handleConfirmClear = async () => {
    setSearchHistory([])
    setIsExpanded(false)
    await appLocalStorage.removeItem(SEARCH_HISTORY_KEY)
    setIsConfirmModalOpen(false)
  }

  // 处理输入自动搜索
  const handleSearch = (value: string) => {
    setSearchValue(value)
    setSuggestions([])
  }

  // 搜索处理函数
  const handleSearchSubmit = async (value: string) => {
    if (value.trim()) {
      await updateSearchHistory(value.trim())
      setSearchValue('')
      onClose()
      reportEvent(TRACK_EVENT.shop_searchpage_product_name_click, {
        product_id: '',
        product_name: value,
      })

      router.push(`/catalogsearch/result?q=${value}`)
    }
  }

  // 切换热搜组
  const toggleHotSearches = () => {
    if (isFetching) {
      return
    }
    handleRefreshClick()
    setIsRotating(true)
    // 动画结束后重置状态
    setTimeout(() => {
      setIsRotating(false)
    }, 300) // 与CSS动画时长保持一致
  }

  useEffect(() => {
    if (isOpen) {
      reportEvent(TRACK_EVENT.shop_search_exposure)
    }
  }, [isOpen, reportEvent])

  // 添加一个通用的检测函数
  const checkHistoryHeight = useCallback(() => {
    if (historyContainerRef.current && searchHistory.length > 0) {
      const containerHeight = historyContainerRef.current.scrollHeight
      const twoRowsHeight = 88
      setNeedsCollapse(containerHeight > twoRowsHeight)
    } else {
      setNeedsCollapse(false)
    }
  }, [searchHistory])

  // 监听弹窗打开状态，确保在弹窗完全打开后检测
  useEffect(() => {
    if (isOpen) {
      // 弹窗打开后，等待一段时间再检测，确保所有内容都已渲染
      const timer = setTimeout(() => {
        checkHistoryHeight()
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [isOpen, checkHistoryHeight])

  // 检测搜索历史是否超过两行（使用通用检测函数）
  useEffect(() => {
    checkHistoryHeight()
  }, [searchHistory, checkHistoryHeight])

  // 使用 ResizeObserver 监听容器尺寸变化
  useEffect(() => {
    if (!historyContainerRef.current || searchHistory.length === 0) return

    const resizeObserver = new ResizeObserver(() => {
      checkHistoryHeight()
    })

    resizeObserver.observe(historyContainerRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [searchHistory, checkHistoryHeight])

  return (
    <>
      <Modal
        className="search-modal"
        open={isOpen}
        onCancel={onClose}
        footer={null}
        closable={false}
        maskClosable={false}
        width="min-content"
        style={{ top: 56 }}
        styles={{
          mask: {
            backdropFilter: 'blur(10px)',
            backgroundColor: 'rgba(0,0,0,0.08)',
            WebkitBackdropFilter: 'blur(10px)',
          },
        }}>
        <SearchInput
          searchValue={searchValue}
          onSearch={handleSearch}
          onChange={handleInputChange}
          onClose={onClose}
          inputRef={inputRef}
          onSubmit={handleSearchSubmit}
          placeholder={defaultSearchWord}
        />

        {/* 搜索联想词列表 */}
        {searchValue && suggestions && suggestions?.length > 0 && (
          <div className="mt-[16px] max-h-[496px] overflow-y-auto bg-white">
            {suggestions.map((item, index) => (
              <button
                key={index}
                className="line-clamp-1 flex h-[46px] w-[600px] items-center px-[24px] text-left hover:rounded-full hover:bg-[#F3F3F4]"
                onClick={() => handleSearchSubmit(item?.words as string)}>
                <div
                  className="font-miSansMedium380 text-[16px] leading-[22px] text-[#444446]"
                  dangerouslySetInnerHTML={{
                    __html: item?.words
                      ? item.words.replace(
                          new RegExp(searchValue, 'gi'),
                          `<span class="text-primary">${searchValue}</span>`,
                        )
                      : '',
                  }}
                />
              </button>
            ))}
          </div>
        )}

        {(!searchValue || !suggestions || suggestions.length === 0) && (
          <>
            {/* 搜索历史 */}
            {searchHistory.length > 0 && (
              <div className="mt-16">
                <div className="mb-8 flex items-center justify-between">
                  <div className="font-miSansDemiBold450 text-[20px] leading-[24px] text-[#000000]">
                    {getI18nString('search_history')}
                  </div>
                  {isDelete ? (
                    <div className="flex flex-row items-center justify-center">
                      <button className="flex flex-row items-center" onClick={clearSearchHistory}>
                        <Trash />
                        <div className="ml-[4px] text-[16px] leading-[22.4px] text-[#6E6E73]">
                          {getI18nString('clear_all')}
                        </div>
                      </button>
                      <div className="mx-[12px] h-[13px] w-[1px] bg-[#86868B]" />
                      <button
                        onClick={() => {
                          setIsDelete(false)
                          handleClear()
                        }}>
                        <div className="font-miSansRegular330 text-[16px] leading-[22.4px] text-[#DA291C]">
                          {getI18nString('done')}
                        </div>
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-row items-center justify-center">
                      <button
                        className="flex items-center hover:text-gray-700"
                        onClick={() => {
                          setIsDelete(true)
                          handleClear()
                        }}>
                        <Trash />
                        <span className="ml-[4px] text-[16px] leading-[22.4px] text-[#6E6E73]">
                          {getI18nString('clear')}
                        </span>
                      </button>
                    </div>
                  )}
                </div>

                <div
                  ref={historyContainerRef}
                  className="flex flex-wrap gap-[12px]"
                  style={{
                    maxHeight: isExpanded || isDelete ? 'none' : '88px',
                    overflow: !isExpanded && !isDelete && needsCollapse ? 'hidden' : 'visible',
                    transition: 'max-height 0.3s ease-in-out',
                  }}>
                  {searchHistory.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        handleSearchSubmit(item)
                      }}
                      className="group flex items-center justify-center rounded-full border border-[#F3F3F4] bg-[#F3F3F4] px-[16px] py-[8px] text-[#000000] hover:border-[#DA291C] hover:bg-[#FEE5E5] hover:text-primary">
                      <span className="line-clamp-1 truncate font-miSansRegular330 text-[16px] leading-[22px]">
                        {item}
                      </span>
                      {isDelete && (
                        <div
                          className="ml-[8px] text-[#86868B] group-hover:text-[#DA291C]"
                          onClick={() => handleItemDelete(item)}>
                          <IconDelete color="currentColor" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
                {!isDelete && needsCollapse && (
                  <div className="mt-base-12 flex justify-center">
                    <button
                      className="flex h-[38px] w-[38px] items-center justify-center rounded-full bg-[#F3F3F4] hover:bg-gray-200"
                      onClick={() => setIsExpanded(!isExpanded)}>
                      <IconArrow color="#000000" size={16} rotate={isExpanded ? 180 : 0} />
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* 热搜区域 */}
            {hotSearchList?.length ? (
              <div className="mt-[48px]">
                <div className="mb-8 flex items-center justify-between">
                  <span className="font-miSansDemiBold450 text-[20px] leading-[24px] text-[#000000]">
                    {getI18nString('ninebot_hot_search')}
                  </span>
                  <button
                    onClick={toggleHotSearches}
                    className="flex items-center text-[#6E6E73] hover:text-gray-700">
                    <Change
                      className={`transition-transform duration-300 ${isRotating ? 'rotate-180' : ''}`}
                    />
                    <span className="ml-[4px] font-miSansMedium380 text-[16px] leading-[140%]">
                      {getI18nString('refresh')}
                    </span>
                  </button>
                </div>
                <div className="grid grid-cols-3 gap-8">
                  {hotSearchList.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        handleSearchSubmit(item?.words as string)
                      }}
                      className="group flex items-center text-left hover:text-primary">
                      <span className="truncate font-miSansMedium380 text-[16px] leading-[140%]">
                        {item?.words}
                      </span>
                      {item?.tag && (
                        <span
                          className="ml-[4px] shrink-0 rounded-full px-base py-[4px] font-miSansDemiBold450 text-[12px] leading-[120%] text-white"
                          style={{
                            backgroundColor: item.color || '#DA291C',
                          }}>
                          {item.tag}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            ) : null}

            {/* 猜你喜欢 */}
            <div className="mt-base-48">
              <RecommendProducts isSearchModal closeModal={onClose} />
            </div>
          </>
        )}
      </Modal>

      <Modal
        title="确认清空"
        open={isConfirmModalOpen}
        onOk={handleConfirmClear}
        onCancel={() => setIsConfirmModalOpen(false)}
        okText="确认"
        cancelText="取消">
        <p>{getI18nString('confirm_delete_history')}</p>
      </Modal>
    </>
  )
}

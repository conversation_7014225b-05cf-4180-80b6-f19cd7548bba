'use client'
import { useCallback, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  cn,
  commonStyles,
  IconPlus,
  mergeStyles,
  NCoinView,
  OrderItem as TOrderItem,
  OrderListItem,
  Price,
  ROUTE,
  sleep,
  useDebounceFn,
  useOrderDetail,
  useTimerCoundown,
  useToastContext,
  useUserOrder,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Button } from 'antd'

import CustomImage from '@/components/common/CustomImage'
import { useMediaQuery } from '@/hooks'

import { Clock, Ellipsis } from './icons'
type TOrderItemProduct = NonNullable<
  NonNullable<NonNullable<TOrderItem>['items']>[number]
>['product']

const ProductImage = ({ item }: { item: TOrderItemProduct }) => (
  <CustomImage
    src={item?.image?.url ?? ''}
    alt={item?.name ?? ''}
    containerClassName="rounded-base flex-none w-[68px] h-[68px] 2xl:w-[98px] 2xl:h-[98px]"
    displayMode="fill"
    priority
  />
)

const OrderItem = ({
  order,
  currentTime,
  updateCancelOrderStatus,
  handleRefresh,
  isLast = false,
}: {
  order: OrderListItem
  currentTime: number
  updateCancelOrderStatus: (number: string) => void
  handleRefresh: () => void
  isLast?: boolean
}) => {
  const getI18nString = useTranslations('Web')
  const [showRemainingProducts, setShowRemainingProducts] = useState(false)

  const responsive = useMediaQuery()
  const { openPage } = useNavigate()
  const toast = useToastContext()

  const { checkOrderPayInfo } = useUserOrder()

  const onEnd = useCallback(() => {
    setTimeout(() => {
      updateCancelOrderStatus(order?.number)
    }, 200)
  }, [order?.number, updateCancelOrderStatus])

  const formattedRes = useTimerCoundown(
    Math.floor(currentTime / 1000),
    order?.status_code === 'pending' ? Number(order?.payment_time_out || 0) : 0,
    1000,
    onEnd,
  )

  const {
    isMigrated,
    migrationOrder,
    migrationProductCount,
    migrationTotalNCoin,
    isReturn,
    hasNCoin,
    isOnlyNCoin,
    isWaitPay,
    isUnPaid,
    hasInvoice,
  } = useOrderDetail(order)

  /**
   * 跳转支付
   */
  const { run: handleRepay } = useDebounceFn(async () => {
    if (order?.encrypt?.nid) {
      const result = await checkOrderPayInfo(order.encrypt.nid)
      // 202 跳转支付
      if (result?.code === 202) {
        openPage({
          route: ROUTE.checkoutPaying,
          queryParams: {
            orderId: order.encrypt.nid,
          },
          replace: true,
        })

        return
      }
      // 200 已支付
      if (result?.code === 200) {
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: getI18nString('order_has_paid'),
        })
        await sleep(500)
        handleRefresh()
        return
      }
      // 支付失败
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: result?.message as string,
      })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 跳转订单详情
   */
  const { run: handleOrderDetail } = useDebounceFn(() => {
    if (order?.encrypt?.nid || order?.id) {
      if (isReturn) {
        openPage({
          route: ROUTE.pcOrderReturn,
          queryParams: {
            returnId: order?.id,
          },
        })
      } else {
        openPage({
          route: ROUTE.accountOrderDetail,
          queryParams: {
            orderNumber: order?.encrypt?.nid || '',
          },
        })
      }
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  const formatTime = useCallback(
    (time: { total: number; days: number; hours: number; minutes: number; seconds: number }) => {
      const { minutes, seconds } = time

      const pad = (num: number) => String(num).padStart(2, '0')
      return [pad(minutes), pad(seconds)]
    },
    [],
  )

  const [minutes, seconds] = formatTime(formattedRes)

  return (
    <>
      <div key={order.id} className={`py-base-32 ${!isLast ? 'border-b border-gray-base' : ''}`}>
        <div className="mb-[16px] flex min-h-16 items-center justify-between 2xl:mb-0 2xl:items-baseline">
          <div className="flex flex-wrap gap-[8px] 2xl:flex-nowrap 2xl:gap-base-32">
            <span className="w-full font-miSansRegular330 text-[14px] leading-[100%] text-[#6E6E73] 2xl:w-auto">
              {getI18nString.rich('order_number', {
                key: isMigrated
                  ? migrationOrder?.order_num
                  : isReturn
                    ? order?.order_number
                    : order?.number,
                s: (chunks) => <span className="text-[#0F0F0F]">{chunks}</span>,
              })}
            </span>
            <span className="font-miSansRegular330 text-[14px] leading-[100%] text-[#0F0F0F]">
              <span className="text-[#6E6E73] 2xl:hidden">{getI18nString('date')}</span>
              {isReturn ? order?.created_at.split(' ')[0] : order?.order_date.split(' ')[0]}
            </span>
          </div>
          <div className="flex items-baseline gap-base-24">
            <span className="whitespace-nowrap font-miSansRegular330 text-[14px] leading-[140%] text-[#6E6E73]">
              {getI18nString.rich('order_qty', {
                key: isMigrated
                  ? migrationProductCount
                  : isReturn
                    ? order?.total_qty
                    : order?.quantity_ordered,
                s: (chunks) => (
                  <span className="font-miSansMedium380 text-[#0F0F0F]">{chunks}</span>
                ),
              })}
            </span>
            <div className="flex gap-[12px]">
              <div className="whitespace-nowrap font-miSansRegular330 text-[16px] text-[#444446]">
                {isReturn
                  ? getI18nString('refund')
                  : isUnPaid
                    ? getI18nString('should_pay')
                    : getI18nString('order_total')}
              </div>
              {isOnlyNCoin ? (
                <NCoinView
                  number={
                    isMigrated
                      ? migrationTotalNCoin
                      : isReturn
                        ? order?.total?.refund_ncoin || 0
                        : order?.ncoin_pay?.grand_total
                  }
                  iconStyle={{ size: 18 }}
                  textStyle={mergeStyles([commonStyles.font_18_bold, 'leading-[22px]'])}
                  showZero
                />
              ) : hasNCoin ? (
                <div className={cn(commonStyles.flex_row, 'flex-shrink-0')}>
                  <Price
                    price={isReturn ? order?.total?.refund_total : order?.total?.grand_total}
                    currencyStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                    textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                    fractionStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                  />
                  <div className="flex items-center">
                    {Number(isReturn ? order?.total?.refund_ncoin : order?.ncoin_pay?.grand_total) >
                      0 && (
                      <>
                        <div className="mx-[4px]">
                          <IconPlus />
                        </div>
                        <NCoinView
                          number={
                            isReturn
                              ? order?.total?.refund_ncoin || 0
                              : order?.ncoin_pay?.grand_total
                          }
                          iconStyle={{ size: 18 }}
                          textStyle={mergeStyles([commonStyles.font_18_bold, 'leading-[22px]'])}
                        />
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <Price
                  price={isReturn ? order?.total?.refund_total : order?.total?.grand_total}
                  color="primary"
                  currencyStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                  textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                  fractionStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                />
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between">
          {order?.items?.length === 1 ? (
            order?.items?.map((item) => (
              <div className="flex items-center gap-base-24" key={item?.id}>
                <ProductImage item={item?.product} />
                <div className="flex flex-col gap-base">
                  <h4 className="font-miSansMedium380 text-[16px] leading-[120%] text-[#0F0F0F]">
                    {item?.product?.name}
                  </h4>
                  {Number(item?.config_selected_options?.length) > 0 && (
                    <div className="flex items-center gap-base">
                      {item?.config_selected_options?.map((item, index) => (
                        <span
                          key={index}
                          className="font-miSansRegular330 text-[14px] leading-[100%] text-[#0F0F0F]">
                          {item?.value_label}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="relative flex items-center gap-base">
              {order?.items
                ?.slice(0, responsive?.xll ? 4 : 3)
                .map((item) => <ProductImage key={item?.id} item={item?.product} />)}
              {Number(order?.items?.length) > (responsive?.xll ? 4 : 3) && (
                <>
                  <div
                    className="relative flex h-16 cursor-pointer items-center justify-center"
                    onMouseEnter={() => setShowRemainingProducts(true)}
                    onMouseLeave={() => setShowRemainingProducts(false)}>
                    <Ellipsis />
                    {showRemainingProducts && (
                      <div
                        className="absolute left-1/2 top-full z-10 mt-2 flex -translate-x-1/2 gap-2 rounded-base bg-white p-2"
                        style={{
                          boxShadow: '0px 0px 32px 0px rgba(0, 0, 0, 0.1)',
                        }}>
                        {order?.items?.slice(responsive?.xll ? 4 : 3).map((item) => (
                          <div
                            key={item?.id}
                            className="h-[68px] w-[68px] overflow-hidden rounded-base bg-[#F8F8F9] 2xl:h-[98px] 2xl:w-[98px]">
                            <CustomImage
                              src={item?.product?.image?.url ?? ''}
                              alt={item?.product?.name ?? ''}
                              containerClassName="w-full h-full"
                              displayMode="fill"
                              priority
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          )}
          <div className="flex items-center justify-end gap-28">
            {isWaitPay ? (
              <div className="flex w-[120px] flex-none flex-col items-center 2xl:w-60">
                <div className="font-miSansMedium380 text-[16px] leading-[100%] text-primary">
                  {getI18nString('unpaid')}
                </div>
                <div className="mt-[4px] flex flex-nowrap items-center gap-[4px]">
                  <Clock />
                  <div className="flex flex-row flex-nowrap items-center font-miSansRegular330 text-[16px] leading-[100%] text-primary">
                    {minutes}
                    {getI18nString('minute')}
                    {seconds}
                    {getI18nString('second')}
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-[120px] text-center font-miSansMedium380 text-[16px] leading-[100%] text-[#000000] 2xl:w-60">
                {isMigrated
                  ? migrationOrder?.status
                  : isReturn
                    ? order?.requisition_type_label
                    : order?.status_tab_label}
              </div>
            )}
            <div className="flex max-w-[112px] flex-wrap justify-end gap-base-12 2xl:min-w-[244px] 2xl:max-w-none 2xl:flex-nowrap">
              {hasInvoice && (
                <Button
                  className="!h-[38px]"
                  style={{ padding: '8px 24px' }}
                  onClick={() => {
                    if (order?.invoice_info?.zlink) {
                      openPage({
                        route: ROUTE.webView,
                        webViewUrl: order.invoice_info.zlink,
                      })
                    }
                  }}>
                  <span className="font-miSansMedium380 text-[16px] leading-[140%] text-[#000000]">
                    {getI18nString('view_invoice')}
                  </span>
                </Button>
              )}
              <Button
                className="!h-[38px]"
                style={{ padding: '8px 24px' }}
                onClick={handleOrderDetail}>
                <span className="font-miSansMedium380 text-[16px] leading-[140%] text-[#000000]">
                  {isReturn ? getI18nString('view_detail') : getI18nString('order_detail')}
                </span>
              </Button>
              {isWaitPay && (
                <Button
                  className="!h-[38px]"
                  style={{ padding: '8px 24px' }}
                  type="primary"
                  onClick={handleRepay}>
                  <span className="font-miSansMedium380 text-[16px] leading-[140%] text-white">
                    {getI18nString('immediate_payment')}
                  </span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default OrderItem

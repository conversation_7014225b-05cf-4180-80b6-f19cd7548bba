import { Address2, House, Phone2 } from './icons'

export default function AddressBar({
  name,
  address,
  phone,
}: {
  name?: string
  address?: string
  phone?: string
}) {
  return (
    <div className="flex items-center justify-between rounded-base bg-[#F8F8F9] px-base-12 py-base font-miSansRegular330 text-[14px] leading-none">
      <div className="flex items-center justify-between gap-base-32">
        <div className="flex items-center gap-base">
          <House />
          {name}
        </div>
        <div className="flex items-center gap-base">
          <Address2 />
          {address}
        </div>
      </div>
      <div className="flex items-center gap-base">
        <Phone2 />
        {phone}
      </div>
    </div>
  )
}

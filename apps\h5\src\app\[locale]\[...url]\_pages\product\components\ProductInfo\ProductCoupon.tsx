import { useCallback, useEffect, useState } from 'react'
import { mergeStyles, ProductCoupons, useLazyGetProductCouponsQuery } from '@ninebot/core'
import { Button } from 'antd-mobile'

import { CustomPopup, CustomTag, ReceiveCouponList } from '@/components'
import { Arrow } from '@/components/icons'

const ProductCoupon = ({ selectedProductId }: { selectedProductId: number }) => {
  const [couponVisible, setCouponVisible] = useState(false)
  const [couponList, setCouponList] = useState<ProductCoupons[]>([])
  const [getProductCoupons] = useLazyGetProductCouponsQuery()

  // 更新优惠券状态 方便后期做一键领券
  const handleCouponListUpdate = useCallback((ruleId: number) => {
    setCouponList((pre) =>
      pre.map((item) => ({
        ...item,
        status: String(item.rule_id) === String(ruleId) ? true : item.status,
      })),
    )
  }, [])

  useEffect(() => {
    if (selectedProductId) {
      getProductCoupons({
        input: { product_id: Number(selectedProductId) },
      })
        .unwrap()
        .then((response) => {
          if (response?.product_avaliable_rules?.avaliable_received) {
            setCouponList(response?.product_avaliable_rules?.avaliable_received as ProductCoupons[])
          }
        })
        .catch((error) => {
          console.log('error', error)
        })
    }
  }, [selectedProductId, getProductCoupons])

  if (couponList?.length === 0) {
    return null
  }

  return (
    <>
      <div className="flex items-center gap-base-24">
        <div className="font-medium">领券</div>
        <div
          className="flex flex-1 items-center justify-between overflow-hidden"
          onClick={() => setCouponVisible(true)}>
          <div className="flex flex-1 items-center gap-base-12 truncate">
            {couponList.map((item) =>
              item?.short_description ? (
                <CustomTag key={item?.rule_id} text={item?.short_description} />
              ) : null,
            )}
          </div>
          <Arrow />
        </div>
      </div>

      {/* 领券弹窗 */}
      <CustomPopup
        visible={couponVisible}
        onClose={() => setCouponVisible(false)}
        showHeader={true}
        headTitle="领券"
        footerClassName="pt-0 pb-[24px]"
        footer={
          couponList.length >= 1 && (
            <Button
              key="confirm"
              className="nb-button w-full"
              color="primary"
              onClick={() => setCouponVisible(false)}>
              确认
            </Button>
          )
        }>
        <div className={mergeStyles(['p-base-24'], couponList.length === 1 ? 'pb-0' : '')}>
          <ReceiveCouponList
            isPdp
            containerStyle={'px-0 mt-0'}
            couponList={couponList as ProductCoupons[]}
            componentId={selectedProductId as number}
            handleCouponListUpdate={handleCouponListUpdate}
          />
        </div>
      </CustomPopup>
    </>
  )
}

export default ProductCoupon
